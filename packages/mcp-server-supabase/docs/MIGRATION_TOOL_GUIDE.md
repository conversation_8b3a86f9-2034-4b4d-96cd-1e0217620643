# Migration Tool Guide

Complete guide for using the Supabase MCP Server Migration Tool for database schema management.

## 📋 Overview

The Migration Tool provides comprehensive database schema management capabilities through both CLI commands and MCP tools. Built on top of `node-pg-migrate`, it offers template-based migration creation, validation, and execution with full integration into the MCP server ecosystem.

## 🚀 Quick Start

### Prerequisites
- Node.js 18+ installed
- Local Supabase instance running
- MCP server configured and connected

### Basic Usage
```bash
# Check connection
mcp-supabase migrate:test

# Create a new migration
mcp-supabase migrate:create add_users_table --template create_table --table-name users

# Run migrations
mcp-supabase migrate:up

# Check status
mcp-supabase migrate:status
```

## 🛠️ Installation & Configuration

### Environment Setup
The migration tool uses your existing Supabase MCP server configuration:

```bash
# Required environment variables
SUPABASE_URL=http://localhost:54321
SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key
DATABASE_URL=postgresql://postgres:postgres@localhost:54322/postgres
```

### Migration Directory Structure
```
supabase/
├── migrations/
│   ├── 20240101120000_create_users_table.sql
│   ├── 20240101130000_add_user_profile_fields.sql
│   └── ...
└── config.toml
```

## 📚 Command Reference

### migrate:create
Create a new migration file with optional templates.

```bash
mcp-supabase migrate:create <name> [options]

Options:
  --template <type>     Migration template (create_table, alter_table, create_index, data_migration, custom)
  --table-name <name>   Table name for template substitution
  --column-name <name>  Column name for template substitution
  --column-type <type>  Column type for template substitution
  --index-name <name>   Index name for template substitution
  --description <desc>  Migration description

Examples:
  mcp-supabase migrate:create create_users_table --template create_table --table-name users
  mcp-supabase migrate:create add_email_index --template create_index --table-name users --index-name idx_users_email
```

### migrate:up
Execute pending migrations.

```bash
mcp-supabase migrate:up [options]

Options:
  --count <n>      Number of migrations to run (default: all)
  --to <name>      Run migrations up to specific migration
  --dry-run        Show what would be executed without running
  --no-lock        Disable migration table locking

Examples:
  mcp-supabase migrate:up                    # Run all pending migrations
  mcp-supabase migrate:up --count 1          # Run only the next migration
  mcp-supabase migrate:up --dry-run          # Preview changes
```

### migrate:down
Rollback migrations.

```bash
mcp-supabase migrate:down [options]

Options:
  --count <n>      Number of migrations to rollback (default: 1)
  --to <name>      Rollback to specific migration
  --dry-run        Show what would be executed without running
  --no-lock        Disable migration table locking

Examples:
  mcp-supabase migrate:down                  # Rollback last migration
  mcp-supabase migrate:down --count 3        # Rollback last 3 migrations
```

### migrate:status
Show migration status and history.

```bash
mcp-supabase migrate:status

Output:
┌─────────────────────────────────────┬─────────┬─────────────────────┐
│ Migration                           │ Status  │ Applied At          │
├─────────────────────────────────────┼─────────┼─────────────────────┤
│ 20240101120000_create_users_table   │ Applied │ 2024-01-01 12:00:00 │
│ 20240101130000_add_user_profiles    │ Pending │ -                   │
└─────────────────────────────────────┴─────────┴─────────────────────┘
```

### migrate:validate
Validate migration files against best practices.

```bash
mcp-supabase migrate:validate [migration-file]

Examples:
  mcp-supabase migrate:validate                                    # Validate all migrations
  mcp-supabase migrate:validate 20240101120000_create_users_table  # Validate specific migration
```

### migrate:test
Test database connection and migration setup.

```bash
mcp-supabase migrate:test

Output:
✅ Database connection successful
✅ Migration table exists
✅ Migration directory accessible
✅ Configuration valid
```

## 📝 Migration Templates

### CREATE_TABLE Template
```sql
-- Migration: Create {tableName} table
-- Description: {description}

-- UP
CREATE TABLE IF NOT EXISTS {tableName} (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Create updated_at trigger
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_{tableName}_updated_at 
    BEFORE UPDATE ON {tableName} 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- DOWN
-- DROP TRIGGER IF EXISTS update_{tableName}_updated_at ON {tableName};
-- DROP FUNCTION IF EXISTS update_updated_at_column();
-- DROP TABLE IF EXISTS {tableName};
```

### ALTER_TABLE Template
```sql
-- Migration: Alter {tableName} table
-- Description: {description}

-- UP
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = '{tableName}' AND column_name = '{columnName}'
    ) THEN
        ALTER TABLE {tableName} ADD COLUMN {columnName} {columnType};
    END IF;
END $$;

-- DOWN
-- ALTER TABLE {tableName} DROP COLUMN IF EXISTS {columnName};
```

### CREATE_INDEX Template
```sql
-- Migration: Create index on {tableName}
-- Description: {description}

-- UP
CREATE INDEX CONCURRENTLY IF NOT EXISTS {indexName} 
ON {tableName} ({columnName});

-- DOWN
-- DROP INDEX IF EXISTS {indexName};
```

## 🔍 Migration File Structure

### Required Sections
Every migration file should include:

1. **Description Comment**: Explains the purpose
2. **UP Section**: Forward migration logic
3. **DOWN Section**: Rollback logic (commented)

### Example Structure
```sql
-- Migration: Create users table with authentication
-- Description: Initial user management schema with auth integration
-- Author: Development Team
-- Date: 2024-01-01

-- UP
BEGIN;

-- Create users table
CREATE TABLE IF NOT EXISTS users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email TEXT UNIQUE NOT NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Add constraints
ALTER TABLE users ADD CONSTRAINT users_email_format 
    CHECK (email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$');

-- Create indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_email ON users (email);

COMMIT;

-- DOWN (Rollback instructions - keep commented)
-- BEGIN;
-- DROP INDEX IF EXISTS idx_users_email;
-- DROP TABLE IF EXISTS users;
-- COMMIT;
```

## ✅ Validation Rules

The migration validator checks for:

1. **Description Comment**: Must include migration description
2. **UP/DOWN Sections**: Both sections should be present
3. **Idempotent Operations**: Use `IF NOT EXISTS` / `IF EXISTS`
4. **Concurrent Indexes**: Use `CONCURRENTLY` for production safety
5. **Audit Fields**: Include `created_at`, `updated_at` for new tables
6. **Filename Format**: Follow timestamp_description.sql pattern
7. **Hardcoded Values**: Avoid hardcoded IDs or sensitive data

### Validation Output
```bash
✅ Migration validation passed (5/5 rules)

Validation Results:
✅ has_description_comment: Migration has proper description
✅ has_up_down_sections: Both UP and DOWN sections present
✅ uses_idempotent_operations: Uses IF NOT EXISTS patterns
✅ uses_concurrent_indexes: Index creation uses CONCURRENTLY
✅ follows_filename_format: Filename follows convention

Suggestions:
💡 Consider adding audit fields (created_at, updated_at) to new tables
💡 Add rollback instructions in DOWN section comments
```

## 🔧 MCP Tool Integration

### Available MCP Tools

#### apply_migration
Apply database migrations through MCP interface.
```json
{
  "name": "apply_migration",
  "arguments": {
    "name": "create_users_table",
    "query": "CREATE TABLE users (id UUID PRIMARY KEY DEFAULT gen_random_uuid());"
  }
}
```

#### list_migrations
List all applied migrations with status.
```json
{
  "name": "list_migrations",
  "arguments": {}
}
```

#### create_migration
Get instructions for creating new migrations.
```json
{
  "name": "create_migration",
  "arguments": {
    "name": "add_user_profiles"
  }
}
```

### Integration with AI Assistants

The migration tools work seamlessly with AI assistants through MCP:

```typescript
// Example: AI assistant creating and applying a migration
const migrationResult = await mcpClient.callTool('apply_migration', {
  name: 'add_user_profiles',
  query: `
    ALTER TABLE users ADD COLUMN IF NOT EXISTS profile_data JSONB;
    CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_profile
    ON users USING GIN (profile_data);
  `
});
```

## 🚨 Troubleshooting

### Common Issues

#### Connection Errors
```bash
Error: Connection failed to database
```
**Solution**: Check DATABASE_URL and ensure Supabase is running
```bash
supabase status
mcp-supabase migrate:test
```

#### Migration Lock Issues
```bash
Error: Migration table is locked
```
**Solution**: Clear lock or use --no-lock flag
```bash
# Check for stuck processes
SELECT * FROM pg_stat_activity WHERE query LIKE '%supabase_migrations%';

# Use no-lock flag (caution: only if no other migrations running)
mcp-supabase migrate:up --no-lock
```

#### Template Variable Errors
```bash
Error: Template variable {tableName} not replaced
```
**Solution**: Provide all required template variables
```bash
mcp-supabase migrate:create add_posts --template create_table --table-name posts
```

#### Validation Failures
```bash
❌ Migration validation failed (3/7 rules)
```
**Solution**: Fix validation issues before applying
```bash
mcp-supabase migrate:validate your_migration.sql
# Fix issues based on output
mcp-supabase migrate:validate your_migration.sql  # Re-validate
```

### Debug Mode
Enable detailed logging for troubleshooting:
```bash
DEBUG=migration:* mcp-supabase migrate:up
```

### Recovery Procedures

#### Stuck Migration
1. Check migration table status
2. Manually complete or rollback
3. Update migration table state

```sql
-- Check current state
SELECT * FROM supabase_migrations ORDER BY id DESC LIMIT 5;

-- Manually mark as complete (if migration actually succeeded)
UPDATE supabase_migrations SET run_on = NOW() WHERE name = 'problematic_migration';

-- Or remove failed entry (if migration failed and needs retry)
DELETE FROM supabase_migrations WHERE name = 'failed_migration';
```

## 📊 Best Practices

### Development Workflow
1. **Create Feature Branch**: `git checkout -b feature/add-user-profiles`
2. **Create Migration**: `mcp-supabase migrate:create add_user_profiles`
3. **Edit Migration File**: Add your schema changes
4. **Validate**: `mcp-supabase migrate:validate`
5. **Test Locally**: `mcp-supabase migrate:up --dry-run`
6. **Apply**: `mcp-supabase migrate:up`
7. **Test Application**: Verify app works with new schema
8. **Commit**: `git add . && git commit -m "Add user profiles migration"`

### Production Deployment
1. **Backup Database**: Always backup before migrations
2. **Test on Staging**: Run migrations on staging environment first
3. **Plan Downtime**: Communicate any required downtime
4. **Monitor Performance**: Watch for performance impacts
5. **Have Rollback Plan**: Know how to rollback if needed

### Migration Naming
- Use descriptive names: `add_user_email_verification`
- Include action: `create_`, `add_`, `remove_`, `update_`
- Be specific: `add_index_users_email` not `add_index`
- Use underscores: `create_user_profiles` not `createUserProfiles`

### Schema Design
- **Always use transactions** for multi-step changes
- **Add indexes concurrently** in production
- **Include audit fields** (`created_at`, `updated_at`)
- **Use appropriate constraints** for data integrity
- **Plan for rollbacks** - avoid irreversible changes
- **Test with real data** before production deployment

## 📚 Additional Resources

- [Migration Examples](../examples/migrations/README.md) - Real-world migration examples
- [Supabase CLI Documentation](https://supabase.com/docs/reference/cli) - Official CLI reference
- [PostgreSQL Migration Best Practices](https://www.postgresql.org/docs/current/ddl-alter.html) - PostgreSQL documentation
- [node-pg-migrate Documentation](https://salsita.github.io/node-pg-migrate/) - Underlying migration library

## 🤝 Contributing

Found an issue or want to improve the migration tool?

1. **Report Issues**: Use GitHub issues for bugs or feature requests
2. **Submit PRs**: Follow the contribution guidelines
3. **Update Documentation**: Help keep docs current and accurate
4. **Share Examples**: Contribute useful migration patterns

---

**Need help?** Check the [troubleshooting section](#-troubleshooting) or join the [Supabase Discord](https://discord.supabase.com) for community support.
