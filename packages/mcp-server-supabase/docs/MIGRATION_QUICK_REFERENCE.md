# Migration Tool Quick Reference

Fast reference for common migration operations with the Supabase MCP Server.

## 🚀 Quick Commands

### Essential Commands
```bash
# Test connection
mcp-supabase migrate:test

# Create migration
mcp-supabase migrate:create <name> --template <type>

# Run migrations
mcp-supabase migrate:up

# Check status
mcp-supabase migrate:status

# Validate migrations
mcp-supabase migrate:validate

# Rollback (careful!)
mcp-supabase migrate:down --count 1
```

## 📝 Common Templates

### Create Table
```bash
mcp-supabase migrate:create create_users --template create_table --table-name users
```

### Add Column
```bash
mcp-supabase migrate:create add_user_email --template alter_table --table-name users --column-name email --column-type TEXT
```

### Create Index
```bash
mcp-supabase migrate:create add_email_index --template create_index --table-name users --column-name email --index-name idx_users_email
```

### Data Migration
```bash
mcp-supabase migrate:create cleanup_users --template data_migration
```

## 🔧 Template Variables

| Variable | Description | Example |
|----------|-------------|---------|
| `{tableName}` | Table name | `users` |
| `{columnName}` | Column name | `email` |
| `{columnType}` | Column type | `TEXT NOT NULL` |
| `{indexName}` | Index name | `idx_users_email` |
| `{description}` | Migration description | `Add user email field` |

## ✅ Validation Checklist

- [ ] Migration has description comment
- [ ] Uses `IF NOT EXISTS` / `IF EXISTS`
- [ ] Indexes use `CONCURRENTLY`
- [ ] Includes audit fields for new tables
- [ ] Has commented rollback instructions
- [ ] Follows naming convention
- [ ] No hardcoded values

## 🚨 Emergency Commands

### Check Migration Status
```bash
mcp-supabase migrate:status
```

### Test Connection
```bash
mcp-supabase migrate:test
```

### Dry Run (Preview Changes)
```bash
mcp-supabase migrate:up --dry-run
```

### Force Unlock (Use with Caution)
```bash
mcp-supabase migrate:up --no-lock
```

## 📊 Common Patterns

### Safe Column Addition
```sql
-- UP
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'users' AND column_name = 'email'
    ) THEN
        ALTER TABLE users ADD COLUMN email TEXT;
    END IF;
END $$;
```

### Concurrent Index Creation
```sql
-- UP
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_email 
ON users (email);
```

### Data Migration with Audit
```sql
-- UP
BEGIN;

-- Create audit table
CREATE TABLE IF NOT EXISTS user_migration_audit (
    id SERIAL PRIMARY KEY,
    user_id UUID,
    action TEXT,
    old_data JSONB,
    new_data JSONB,
    migrated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Perform data migration
WITH updated_users AS (
    UPDATE users 
    SET email = LOWER(TRIM(email))
    WHERE email IS NOT NULL
    RETURNING id, email
)
INSERT INTO user_migration_audit (user_id, action, new_data)
SELECT id, 'email_normalized', jsonb_build_object('email', email)
FROM updated_users;

COMMIT;
```

## 🔍 Troubleshooting Quick Fixes

### Connection Issues
```bash
# Check Supabase status
supabase status

# Test migration connection
mcp-supabase migrate:test

# Check environment variables
echo $DATABASE_URL
```

### Lock Issues
```sql
-- Check for locks
SELECT * FROM pg_stat_activity WHERE query LIKE '%supabase_migrations%';

-- Kill stuck process (get pid from above query)
SELECT pg_terminate_backend(<pid>);
```

### Failed Migration Recovery
```sql
-- Check migration table
SELECT * FROM supabase_migrations ORDER BY id DESC LIMIT 5;

-- Remove failed migration entry
DELETE FROM supabase_migrations WHERE name = 'failed_migration_name';
```

## 📚 File Structure Reference

```
supabase/
├── migrations/
│   ├── 20240101120000_create_users_table.sql
│   ├── 20240101130000_add_user_email.sql
│   └── 20240101140000_create_email_index.sql
└── config.toml
```

## 🎯 MCP Integration

### Apply Migration via MCP
```json
{
  "tool": "apply_migration",
  "arguments": {
    "name": "add_user_profiles",
    "query": "ALTER TABLE users ADD COLUMN profile JSONB;"
  }
}
```

### List Migrations via MCP
```json
{
  "tool": "list_migrations",
  "arguments": {}
}
```

## ⚡ Performance Tips

1. **Use CONCURRENTLY** for index creation in production
2. **Batch large data changes** to avoid long locks
3. **Create indexes after bulk inserts** for better performance
4. **Use partial indexes** for filtered queries
5. **Monitor query performance** with EXPLAIN ANALYZE

## 🔐 Security Reminders

- Never commit sensitive data in migrations
- Use environment variables for configuration
- Test rollback procedures before production
- Backup database before major migrations
- Validate data integrity after migrations

## 📞 Getting Help

- **Documentation**: [Full Migration Guide](./MIGRATION_TOOL_GUIDE.md)
- **Examples**: [Migration Examples](../examples/migrations/)
- **Issues**: GitHub repository issues
- **Community**: [Supabase Discord](https://discord.supabase.com)

---

**Pro Tip**: Always test migrations on a copy of production data before deploying!
